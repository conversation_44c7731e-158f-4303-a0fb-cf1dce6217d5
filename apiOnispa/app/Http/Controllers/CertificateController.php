<?php
namespace App\Http\Controllers;

use App\Models\Certificate;
use Illuminate\Http\Request;
use App\Providers\TracesNtChedClient;
use App\Services\ApiLogService;

class CertificateController extends Controller
{
    public function index()
    {
        $certificates = Certificate::with('validator')->orderBy('created_at', 'desc')->paginate(20);
        
        // Get the last certificate date for guidance
        $lastCertificate = Certificate::orderBy('created_at', 'desc')->first();
        $lastCertificateDate = $lastCertificate ? $lastCertificate->created_at->format('Y-m-d') : null;
        
        // Get the earliest certificate date
        $earliestCertificate = Certificate::orderBy('created_at', 'asc')->first();
        $earliestCertificateDate = $earliestCertificate ? $earliestCertificate->created_at->format('Y-m-d') : null;
        
        // Get certificate count by date range
        $todayCount = Certificate::whereDate('created_at', today())->count();
        $yesterdayCount = Certificate::whereDate('created_at', today()->subDay())->count();
        $lastWeekCount = Certificate::whereBetween('created_at', [today()->subWeek(), today()])->count();
        
        return view('certificates.index', compact(
            'certificates', 
            'lastCertificateDate', 
            'earliestCertificateDate',
            'todayCount',
            'yesterdayCount',
            'lastWeekCount'
        ));
    }

    public function show($id)
    {
        $certificate = Certificate::findOrFail($id);
        return response()->json($certificate);
    }

    public function fetchAndStore(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $startTime = microtime(true);

        try {
            // Get TRACES configuration from environment
            $tracesConfig = config('services.traces');

            // Log configuration for debugging (without sensitive data)
            \Log::info('TRACES API Configuration', [
                'username' => $tracesConfig['username'],
                'client_id' => $tracesConfig['client_id'],
                'use_production' => $tracesConfig['use_production'],
                'timeout' => $tracesConfig['timeout'],
                'verify_ssl' => $tracesConfig['verify_ssl'],
                'auth_key_length' => strlen($tracesConfig['auth_key']),
                'server_timezone' => date_default_timezone_get(),
                'server_time' => date('Y-m-d H:i:s T'),
                'correct_time_should_be' => date('Y-m-d H:i:s T', time() - (365 * 24 * 60 * 60)), // Subtract 1 year
                'php_version' => PHP_VERSION,
                'curl_version' => curl_version()['version'] ?? 'unknown',
                'system_date_command' => trim(shell_exec('date') ?? 'unavailable'),
                'ntp_status' => trim(shell_exec('timedatectl status 2>/dev/null | grep "NTP synchronized"') ?? 'unavailable')
            ]);

            // Create TracesNtChedClient with environment configuration
            $client = new TracesNtChedClient(
                $tracesConfig['username'],
                $tracesConfig['auth_key'],
                $tracesConfig['client_id'],
                $tracesConfig['use_production']
            );

            $certificates = $client->getValidatedFishCertificates($startDate, $endDate);
            $duration = round((microtime(true) - $startTime) * 1000, 2); // Convert to milliseconds

            $storedCount = 0;
            foreach ($certificates as $cert) {
                Certificate::updateOrCreate(
                    ['certificate_id' => $cert['id']],
                    [
                        'type' => $cert['type'],
                        'local_reference' => $cert['local_reference'],
                        'status' => $cert['status'],
                        'status_name' => $cert['status_name'],
                        'bcp_code' => $cert['bcp_code'],
                        'bcp_unlocode' => $cert['bcp_unlocode'],
                        'country_of_issuance' => $cert['country_of_issuance'],
                        'country_of_entry' => $cert['country_of_entry'],
                        'country_of_dispatch' => $cert['country_of_dispatch'],
                        'country_of_origin' => $cert['country_of_origin'],
                        'country_of_place_of_destination' => $cert['country_of_place_of_destination'],
                        'country_of_consignor' => $cert['country_of_consignor'],
                        'consignor_name' => $cert['consignor_name'],
                        'country_of_consignee' => $cert['country_of_consignee'],
                        'consignee_name' => $cert['consignee_name'],
                        'create_date_time' => $cert['create_date_time'],
                        'update_date_time' => $cert['update_date_time'],
                        'status_change_date_time' => $cert['status_change_date_time'],
                        'declaration_date_time' => $cert['declaration_date_time'],
                        'decision_date_time' => $cert['decision_date_time'],
                        'prior_notification_date_time' => $cert['prior_notification_date_time'],
                        'commodities' => $cert['commodities'],
                        'references' => $cert['references'],
                    ]
                );
                $storedCount++;
            }

            // Log the successful TRACES API call
            ApiLogService::logTracesApiCall(
                $startDate, 
                $endDate, 
                $certificates, 
                $duration, 
                true, 
                null, 
                200
            );

            // Log the successful fetch
            \Log::info("Successfully fetched and stored {$storedCount} certificates from TRACES API", [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'stored_count' => $storedCount,
                'duration_ms' => $duration
            ]);

            if ($request->expectsJson()) {
                return response()->json(['stored' => $storedCount, 'success' => true]);
            }

            return redirect()->route('certificates')->with('success', "Successfully fetched and stored {$storedCount} certificates.");

        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            // Log the failed TRACES API call
            ApiLogService::logTracesApiCall(
                $startDate, 
                $endDate, 
                [], 
                $duration, 
                false, 
                $e->getMessage(), 
                500
            );

            \Log::error('Error fetching certificates from TRACES API', [
                'error' => $e->getMessage(),
                'start_date' => $startDate,
                'end_date' => $endDate,
                'duration_ms' => $duration
            ]);

            if ($request->expectsJson()) {
                return response()->json(['error' => $e->getMessage()], 500);
            }

            return redirect()->route('certificates')->with('error', 'Error fetching certificates: ' . $e->getMessage());
        }
    }

    public function sendSelected(Request $request)
    {
        $selectedIds = $request->input('selected_certificates', []);
        
        if (empty($selectedIds)) {
            return redirect()->route('certificates')->with('error', 'No certificates selected.');
        }



        $selectedIds = json_decode($selectedIds, true);
        
        $certificates = Certificate::whereIn('id', $selectedIds)->get();
        
        try {
            // Here you would implement the logic to send certificates back to TRACES API
            // For now, we'll just log the action
            \Log::info("Sending selected certificates back to TRACES API", [
                'certificate_ids' => $selectedIds,
                'count' => count($certificates)
            ]);

            // Update status to indicate they've been sent and track who validated them
            Certificate::whereIn('id', $selectedIds)->update([
                'status' => 'sent',
                'validated_by' => auth()->id(),
                'validated_at' => now()
            ]);

            return redirect()->route('certificates')->with('success', "Successfully sent " . count($certificates) . " certificates to TRACES API.");

        } catch (\Exception $e) {
            \Log::error('Error sending certificates to TRACES API', [
                'error' => $e->getMessage(),
                'certificate_ids' => $selectedIds
            ]);

            return redirect()->route('certificates')->with('error', 'Error sending certificates: ' . $e->getMessage());
        }
    }

    /**
     * Get all products linked to a certificate by its reference number
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCertificateProducts(Request $request)
    {
        // Validate the certificate reference format
        $validator = \Validator::make($request->all(), [
            'reference' => 'required|string'
        ]);


        $chedReference = $request->input('reference');

        try {
            \Log::info('Retrieving certificate products directly from TRACES API', ['reference' => $chedReference]);

            // Get TRACES API configuration
            $tracesConfig = config('services.traces', []);
            if (empty($tracesConfig['username']) || empty($tracesConfig['auth_key'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'TRACES API configuration not found. Please configure API parameters.',
                    'error_code' => 'CONFIGURATION_MISSING'
                ], 500);
            }


            // dd($tracesConfig);

            // Create TracesNtChedClient with environment configuration
            $client = new \App\Providers\TracesNtChedClient(
                $tracesConfig['username'],
                $tracesConfig['auth_key'],
                $tracesConfig['client_id'],
                $tracesConfig['use_production']
            );

            // Use smart reference detection to handle both CHED IDs and sanitary certificate references
            $result = $client->getCertificateByAnyReference($chedReference);

            // Handle both single certificate and multiple certificates
            if (isset($result['id'])) {
                // Single certificate returned
                $certificate = $result;
                $products = $this->extractProductsFromTracesData($certificate);
            } else if (is_array($result) && count($result) > 0) {
                // Multiple certificates returned - use the first one
                $certificate = $result[0];
                $products = $this->extractProductsFromTracesData($certificate);

                \Log::info('Multiple certificates found for reference, using first one', [
                    'reference' => $chedReference,
                    'total_certificates' => count($result),
                    'selected_certificate' => $certificate['id'] ?? 'unknown'
                ]);
            } else {
                throw new \Exception('No certificate data returned');
            }

            \Log::info('Certificate products retrieved successfully from TRACES API', [
                'reference' => $chedReference,
                'products_count' => count($products)
            ]);

            return response()->json([
                'success' => true,
                'certificate_reference' => $chedReference,
                'source' => 'traces_api',
                'products_count' => count($products),
                'products' => $products
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to retrieve certificate products', [
                'reference' => $chedReference,
                'error' => $e->getMessage()
            ]);

            // Handle specific TRACES NT errors
            if (strpos($e->getMessage(), 'ChedCertificateNotFoundException') !== false) {
                return response()->json([
                    'success' => false,
                    'message' => 'Certificate not found',
                    'error_code' => 'CERTIFICATE_NOT_FOUND'
                ], 404);
            }

            if (strpos($e->getMessage(), 'ChedCertificatePermissionDeniedException') !== false) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied to this certificate',
                    'error_code' => 'PERMISSION_DENIED'
                ], 403);
            }

            if (strpos($e->getMessage(), 'Authentication failed') !== false) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication error with TRACES NT',
                    'error_code' => 'AUTHENTICATION_ERROR'
                ], 401);
            }

            // Generic error
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve certificate products: ' . $e->getMessage(),
                'error_code' => 'RETRIEVAL_ERROR'
            ], 500);
        }
    }



    /**
     * Extract products from TRACES API certificate data
     *
     * @param array $certificate
     * @return array
     */
    private function extractProductsFromTracesData(array $certificate)
    {
        $products = [];
        $consignments = $certificate['consignments'] ?? [];

        \Log::info('Extracting products from certificate data', [
            'consignments_count' => count($consignments),
            'certificate_keys' => array_keys($certificate)
        ]);

        foreach ($consignments as $consignmentIndex => $consignment) {
            $consignmentItems = $consignment['consignment_items'] ?? [];

            \Log::info("Processing consignment $consignmentIndex", [
                'consignment_items_count' => count($consignmentItems),
                'consignment_keys' => array_keys($consignment)
            ]);

            foreach ($consignmentItems as $itemIndex => $item) {
                $sequence = $item['sequence_numeric'] ?? count($products) + 1;
                $classifications = $item['classifications'] ?? [];

                \Log::info("Processing item $itemIndex", [
                    'sequence' => $sequence,
                    'classifications_count' => count($classifications),
                    'item_keys' => array_keys($item)
                ]);

                // Skip sequence 0 as it's usually the totals/summary
                if ($sequence == 0) {
                    \Log::info("Skipping sequence 0 (totals/summary)");
                    continue;
                }

                foreach ($classifications as $classIndex => $classification) {
                    \Log::info("Processing classification $classIndex", [
                        'system_id' => $classification['system_id'] ?? null,
                        'class_code' => $classification['class_code'] ?? null
                    ]);

                    $products[] = [
                        'sequence' => $sequence,
                        'system_id' => $classification['system_id'] ?? null,
                        'system_name' => $classification['system_name'] ?? null,
                        'classification_code' => $classification['class_code'] ?? null,
                        'description' => $classification['class_names']['en'] ?? null,
                        'descriptions' => $classification['class_names'] ?? [],
                        'scientific_name' => $item['scientific_name'] ?? null,
                        'net_weight' => $item['net_weight'] ?? null,
                        'item_description' => $item['description'] ?? null,
                        'source' => 'traces_api'
                    ];
                }

                // If no classifications found, still add the item with basic info
                if (empty($classifications)) {
                    \Log::info("No classifications found for item, adding basic info");
                    $products[] = [
                        'sequence' => $sequence,
                        'system_id' => null,
                        'system_name' => null,
                        'classification_code' => null,
                        'description' => $item['description'] ?? 'No classification data available',
                        'descriptions' => [],
                        'scientific_name' => $item['scientific_name'] ?? null,
                        'net_weight' => $item['net_weight'] ?? null,
                        'item_description' => $item['description'] ?? null,
                        'source' => 'traces_api'
                    ];
                }
            }
        }

        \Log::info('Product extraction completed', ['products_count' => count($products)]);
        return $products;
    }
}
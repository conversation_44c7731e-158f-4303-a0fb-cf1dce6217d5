<?php
namespace App\Http\Controllers;

use App\Models\Certificate;
use Illuminate\Http\Request;
use App\Providers\TracesNtChedClient;
use App\Services\ApiLogService;

class CertificateController extends Controller
{
    public function index()
    {
        $certificates = Certificate::with('validator')->orderBy('created_at', 'desc')->paginate(20);
        
        // Get the last certificate date for guidance
        $lastCertificate = Certificate::orderBy('created_at', 'desc')->first();
        $lastCertificateDate = $lastCertificate ? $lastCertificate->created_at->format('Y-m-d') : null;
        
        // Get the earliest certificate date
        $earliestCertificate = Certificate::orderBy('created_at', 'asc')->first();
        $earliestCertificateDate = $earliestCertificate ? $earliestCertificate->created_at->format('Y-m-d') : null;
        
        // Get certificate count by date range
        $todayCount = Certificate::whereDate('created_at', today())->count();
        $yesterdayCount = Certificate::whereDate('created_at', today()->subDay())->count();
        $lastWeekCount = Certificate::whereBetween('created_at', [today()->subWeek(), today()])->count();
        
        return view('certificates.index', compact(
            'certificates', 
            'lastCertificateDate', 
            'earliestCertificateDate',
            'todayCount',
            'yesterdayCount',
            'lastWeekCount'
        ));
    }

    public function show($id)
    {
        $certificate = Certificate::findOrFail($id);
        return response()->json($certificate);
    }

    public function fetchAndStore(Request $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $startTime = microtime(true);

        try {
            // Get TRACES configuration from environment
            $tracesConfig = config('services.traces');

            // Log configuration for debugging (without sensitive data)
            \Log::info('TRACES API Configuration', [
                'username' => $tracesConfig['username'],
                'client_id' => $tracesConfig['client_id'],
                'use_production' => $tracesConfig['use_production'],
                'timeout' => $tracesConfig['timeout'],
                'verify_ssl' => $tracesConfig['verify_ssl'],
                'auth_key_length' => strlen($tracesConfig['auth_key']),
                'server_timezone' => date_default_timezone_get(),
                'server_time' => date('Y-m-d H:i:s T'),
                'correct_time_should_be' => date('Y-m-d H:i:s T', time() - (365 * 24 * 60 * 60)), // Subtract 1 year
                'php_version' => PHP_VERSION,
                'curl_version' => curl_version()['version'] ?? 'unknown',
                'system_date_command' => trim(shell_exec('date') ?? 'unavailable'),
                'ntp_status' => trim(shell_exec('timedatectl status 2>/dev/null | grep "NTP synchronized"') ?? 'unavailable')
            ]);

            // Create TracesNtChedClient with environment configuration
            $client = new TracesNtChedClient(
                $tracesConfig['username'],
                $tracesConfig['auth_key'],
                $tracesConfig['client_id'],
                $tracesConfig['use_production']
            );

            $certificates = $client->getValidatedFishCertificates($startDate, $endDate);
            $duration = round((microtime(true) - $startTime) * 1000, 2); // Convert to milliseconds

            $storedCount = 0;
            foreach ($certificates as $cert) {
                Certificate::updateOrCreate(
                    ['certificate_id' => $cert['id']],
                    [
                        'type' => $cert['type'],
                        'local_reference' => $cert['local_reference'],
                        'status' => $cert['status'],
                        'status_name' => $cert['status_name'],
                        'bcp_code' => $cert['bcp_code'],
                        'bcp_unlocode' => $cert['bcp_unlocode'],
                        'country_of_issuance' => $cert['country_of_issuance'],
                        'country_of_entry' => $cert['country_of_entry'],
                        'country_of_dispatch' => $cert['country_of_dispatch'],
                        'country_of_origin' => $cert['country_of_origin'],
                        'country_of_place_of_destination' => $cert['country_of_place_of_destination'],
                        'country_of_consignor' => $cert['country_of_consignor'],
                        'consignor_name' => $cert['consignor_name'],
                        'country_of_consignee' => $cert['country_of_consignee'],
                        'consignee_name' => $cert['consignee_name'],
                        'create_date_time' => $cert['create_date_time'],
                        'update_date_time' => $cert['update_date_time'],
                        'status_change_date_time' => $cert['status_change_date_time'],
                        'declaration_date_time' => $cert['declaration_date_time'],
                        'decision_date_time' => $cert['decision_date_time'],
                        'prior_notification_date_time' => $cert['prior_notification_date_time'],
                        'commodities' => $cert['commodities'],
                        'references' => $cert['references'],
                    ]
                );
                $storedCount++;
            }

            // Log the successful TRACES API call
            ApiLogService::logTracesApiCall(
                $startDate, 
                $endDate, 
                $certificates, 
                $duration, 
                true, 
                null, 
                200
            );

            // Log the successful fetch
            \Log::info("Successfully fetched and stored {$storedCount} certificates from TRACES API", [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'stored_count' => $storedCount,
                'duration_ms' => $duration
            ]);

            if ($request->expectsJson()) {
                return response()->json(['stored' => $storedCount, 'success' => true]);
            }

            return redirect()->route('certificates')->with('success', "Successfully fetched and stored {$storedCount} certificates.");

        } catch (\Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            // Log the failed TRACES API call
            ApiLogService::logTracesApiCall(
                $startDate, 
                $endDate, 
                [], 
                $duration, 
                false, 
                $e->getMessage(), 
                500
            );

            \Log::error('Error fetching certificates from TRACES API', [
                'error' => $e->getMessage(),
                'start_date' => $startDate,
                'end_date' => $endDate,
                'duration_ms' => $duration
            ]);

            if ($request->expectsJson()) {
                return response()->json(['error' => $e->getMessage()], 500);
            }

            return redirect()->route('certificates')->with('error', 'Error fetching certificates: ' . $e->getMessage());
        }
    }

    public function sendSelected(Request $request)
    {
        $selectedIds = $request->input('selected_certificates', []);
        
        if (empty($selectedIds)) {
            return redirect()->route('certificates')->with('error', 'No certificates selected.');
        }



        $selectedIds = json_decode($selectedIds, true);
        
        $certificates = Certificate::whereIn('id', $selectedIds)->get();
        
        try {
            // Here you would implement the logic to send certificates back to TRACES API
            // For now, we'll just log the action
            \Log::info("Sending selected certificates back to TRACES API", [
                'certificate_ids' => $selectedIds,
                'count' => count($certificates)
            ]);

            // Update status to indicate they've been sent and track who validated them
            Certificate::whereIn('id', $selectedIds)->update([
                'status' => 'sent',
                'validated_by' => auth()->id(),
                'validated_at' => now()
            ]);

            return redirect()->route('certificates')->with('success', "Successfully sent " . count($certificates) . " certificates to TRACES API.");

        } catch (\Exception $e) {
            \Log::error('Error sending certificates to TRACES API', [
                'error' => $e->getMessage(),
                'certificate_ids' => $selectedIds
            ]);

            return redirect()->route('certificates')->with('error', 'Error sending certificates: ' . $e->getMessage());
        }
    }

    /**
     * Get all products linked to a certificate by its reference number
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCertificateProducts(Request $request)
    {
        $reference = $request->input('reference');

        if (!$reference) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate reference is required',
                'error_code' => 'MISSING_REFERENCE'
            ], 400);
        }

        // Validate reference format (basic validation)
        if (strlen($reference) < 5) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid certificate reference format',
                'error_code' => 'INVALID_REFERENCE_FORMAT'
            ], 400);
        }

        try {
            \Log::info('Retrieving certificate products from database', [
                'reference' => $reference
            ]);

            // Try to find certificate by CHED ID first
            $certificate = \App\Models\ChedCertificate::where('ched_id', $reference)
                ->with(['products', 'sanitaryReferences'])
                ->first();

            // If not found by CHED ID, try to find by sanitary reference
            if (!$certificate) {
                $certificate = \App\Models\ChedCertificate::findBySanitaryReference($reference);
            }

            // If not found in database, try to fetch from TRACES API and store it
            if (!$certificate) {
                \Log::info('Certificate not found in database, trying TRACES API fallback', [
                    'reference' => $reference
                ]);

                try {
                    $certificate = $this->fetchAndStoreCertificateFromApi($reference);

                    if (!$certificate) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Certificate not found in database or TRACES API.',
                            'error_code' => 'CERTIFICATE_NOT_FOUND',
                            'suggestion' => 'Verify the reference number is correct'
                        ], 404);
                    }

                    \Log::info('Certificate fetched from TRACES API and stored in database', [
                        'reference' => $reference,
                        'ched_id' => $certificate->ched_id
                    ]);

                } catch (\Exception $e) {
                    \Log::error('Failed to fetch certificate from TRACES API', [
                        'reference' => $reference,
                        'error' => $e->getMessage()
                    ]);

                    return response()->json([
                        'success' => false,
                        'message' => 'Certificate not found in database. TRACES API fallback failed: ' . $e->getMessage(),
                        'error_code' => 'API_FALLBACK_FAILED',
                        'suggestion' => 'Try running: php artisan ched:sync --recent'
                    ], 404);
                }
            }

            // Extract products from the certificate
            $products = $certificate->products->map(function ($product) {
                return [
                    'system_id' => $product->system_id,
                    'system_name' => $product->system_name,
                    'class_code' => $product->class_code,
                    'class_names' => $product->class_names,
                    'class_name_en' => $product->class_name_en,
                ];
            })->toArray();

            // Get sanitary references for additional context
            $sanitaryReferences = $certificate->sanitaryReferences->map(function ($ref) {
                return [
                    'sanitary_reference' => $ref->sanitary_reference,
                    'type_name' => $ref->type_name,
                    'attachment_uri' => $ref->attachment_uri,
                ];
            })->toArray();

            // Determine data source
            $dataSource = $certificate->wasRecentlyCreated ? 'api_fallback' : 'database';

            \Log::info('Certificate products retrieved successfully', [
                'reference' => $reference,
                'ched_id' => $certificate->ched_id,
                'source' => $dataSource,
                'products_count' => count($products),
                'sanitary_references_count' => count($sanitaryReferences)
            ]);

            return response()->json([
                'success' => true,
                'certificate_reference' => $reference,
                'ched_id' => $certificate->ched_id,
                'source' => $dataSource,
                'certificate_info' => [
                    'type' => $certificate->type,
                    'status' => $certificate->status_name,
                    'country_of_dispatch' => $certificate->country_of_dispatch,
                    'consignor_name' => $certificate->consignor_name,
                    'consignee_name' => $certificate->consignee_name,
                    'update_date_time' => $certificate->update_date_time?->toISOString(),
                ],
                'products_count' => count($products),
                'products' => $products,
                'sanitary_references' => $sanitaryReferences,
                'last_sync_info' => $this->getLastSyncInfo(),
                'note' => $dataSource === 'api_fallback' ? 'Certificate was fetched from TRACES API and stored in database for future use.' : null
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to retrieve certificate products from database', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve certificate products: ' . $e->getMessage(),
                'error_code' => 'RETRIEVAL_FAILED'
            ], 500);
        }
    }

    /**
     * Fetch certificate from TRACES API and store in database
     */
    private function fetchAndStoreCertificateFromApi(string $reference): ?\App\Models\ChedCertificate
    {
        try {
            // Get TRACES API configuration
            $tracesConfig = config('services.traces', []);
            if (empty($tracesConfig['username']) || empty($tracesConfig['auth_key'])) {
                throw new \Exception('TRACES API configuration not found');
            }

            // Create TracesNtChedClient
            $client = new \App\Providers\TracesNtChedClient(
                $tracesConfig['username'],
                $tracesConfig['auth_key'],
                $tracesConfig['client_id'],
                $tracesConfig['use_production']
            );

            // Use smart reference detection to handle both CHED IDs and sanitary certificate references
            $result = $client->getCertificateByAnyReference($reference);


            

            // Handle both single certificate and multiple certificates
            $certificateData = null;
            if (isset($result['id'])) {
                // Single certificate returned
                $certificateData = $result;
            } else if (is_array($result) && count($result) > 0) {
                // Multiple certificates returned - use the first one
                $certificateData = $result[0];

                \Log::info('Multiple certificates found for reference, using first one', [
                    'reference' => $reference,
                    'total_certificates' => count($result),
                    'selected_certificate' => $certificateData['id'] ?? 'unknown'
                ]);
            }

            if (!$certificateData) {
                return null;
            }

            // Store the certificate in database using the sync service logic
            return $this->storeCertificateFromApiData($certificateData);

        } catch (\Exception $e) {
            \Log::error('Failed to fetch certificate from TRACES API', [
                'reference' => $reference,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Store certificate data from API into database
     */
    private function storeCertificateFromApiData(array $data): \App\Models\ChedCertificate
    {
        $chedId = $data['id'] ?? null;
        if (!$chedId) {
            throw new \Exception('Certificate data missing ID');
        }

        // Use database transaction to ensure data consistency
        return \DB::transaction(function () use ($data, $chedId) {
            // Check if certificate already exists (might have been created by another request)
            $certificate = \App\Models\ChedCertificate::where('ched_id', $chedId)->first();

            if ($certificate) {
                // Update existing certificate
                $certificate->update([
                    'type' => $data['type'] ?? $certificate->type,
                    'local_reference' => $data['local_reference'] ?? $certificate->local_reference,
                    'status' => $data['status'] ?? $certificate->status,
                    'status_name' => $data['status_name'] ?? $certificate->status_name,
                    'bcp_code' => $data['bcp_code'] ?? $certificate->bcp_code,
                    'bcp_unlocode' => $data['bcp_unlocode'] ?? $certificate->bcp_unlocode,
                    'country_of_issuance' => $data['country_of_issuance'] ?? $certificate->country_of_issuance,
                    'country_of_entry' => $data['country_of_entry'] ?? $certificate->country_of_entry,
                    'country_of_dispatch' => $data['country_of_dispatch'] ?? $certificate->country_of_dispatch,
                    'country_of_origin' => $data['country_of_origin'] ?? $certificate->country_of_origin,
                    'country_of_place_of_destination' => $data['country_of_place_of_destination'] ?? $certificate->country_of_place_of_destination,
                    'country_of_consignor' => $data['country_of_consignor'] ?? $certificate->country_of_consignor,
                    'consignor_name' => $data['consignor_name'] ?? $certificate->consignor_name,
                    'country_of_consignee' => $data['country_of_consignee'] ?? $certificate->country_of_consignee,
                    'consignee_name' => $data['consignee_name'] ?? $certificate->consignee_name,
                    'update_date_time' => $this->parseDateTime($data['update_date_time'] ?? null) ?? $certificate->update_date_time,
                    'status_change_date_time' => $this->parseDateTime($data['status_change_date_time'] ?? null) ?? $certificate->status_change_date_time,
                    'declaration_date_time' => $this->parseDateTime($data['declaration_date_time'] ?? null) ?? $certificate->declaration_date_time,
                    'decision_date_time' => $this->parseDateTime($data['decision_date_time'] ?? null) ?? $certificate->decision_date_time,
                    'prior_notification_date_time' => $this->parseDateTime($data['prior_notification_date_time'] ?? null) ?? $certificate->prior_notification_date_time,
                    'raw_data' => $data,
                ]);
            } else {
                // Create new certificate
                $certificate = \App\Models\ChedCertificate::create([
                    'ched_id' => $chedId,
                    'type' => $data['type'] ?? null,
                    'local_reference' => $data['local_reference'] ?? null,
                    'status' => $data['status'] ?? null,
                    'status_name' => $data['status_name'] ?? null,
                    'bcp_code' => $data['bcp_code'] ?? null,
                    'bcp_unlocode' => $data['bcp_unlocode'] ?? null,
                    'country_of_issuance' => $data['country_of_issuance'] ?? null,
                    'country_of_entry' => $data['country_of_entry'] ?? null,
                    'country_of_dispatch' => $data['country_of_dispatch'] ?? null,
                    'country_of_origin' => $data['country_of_origin'] ?? null,
                    'country_of_place_of_destination' => $data['country_of_place_of_destination'] ?? null,
                    'country_of_consignor' => $data['country_of_consignor'] ?? null,
                    'consignor_name' => $data['consignor_name'] ?? null,
                    'country_of_consignee' => $data['country_of_consignee'] ?? null,
                    'consignee_name' => $data['consignee_name'] ?? null,
                    'create_date_time' => $this->parseDateTime($data['create_date_time'] ?? null),
                    'update_date_time' => $this->parseDateTime($data['update_date_time'] ?? null),
                    'status_change_date_time' => $this->parseDateTime($data['status_change_date_time'] ?? null),
                    'declaration_date_time' => $this->parseDateTime($data['declaration_date_time'] ?? null),
                    'decision_date_time' => $this->parseDateTime($data['decision_date_time'] ?? null),
                    'prior_notification_date_time' => $this->parseDateTime($data['prior_notification_date_time'] ?? null),
                    'raw_data' => $data,
                ]);
            }

            // Process sanitary references
            $this->processSanitaryReferencesFromApi($certificate, $data);

            // Process products
            $this->processProductsFromApi($certificate, $data);

            // Load relationships for return
            return $certificate->load(['products', 'sanitaryReferences']);
        });
    }

    /**
     * Process sanitary references from API data
     */
    private function processSanitaryReferencesFromApi(\App\Models\ChedCertificate $certificate, array $data): void
    {
        if (!isset($data['references']) || !is_array($data['references'])) {
            return;
        }

        foreach ($data['references'] as $reference) {
            // Only process sanitary certificate references (type_code 852)
            if (($reference['type_code'] ?? null) === '852' ||
                strpos($reference['id'] ?? '', 'IMPORT.EU.') === 0) {

                $sanitaryRef = $reference['id'] ?? null;
                if (!$sanitaryRef) continue;

                // Check if this sanitary reference already exists for this certificate
                $exists = \App\Models\ChedSanitaryReference::where('ched_id', $certificate->ched_id)
                    ->where('sanitary_reference', $sanitaryRef)
                    ->exists();

                if (!$exists) {
                    \App\Models\ChedSanitaryReference::create([
                        'ched_id' => $certificate->ched_id,
                        'sanitary_reference' => $sanitaryRef,
                        'type_code' => $reference['type_code'] ?? null,
                        'type_name' => $reference['type_name'] ?? null,
                        'relationship_type' => $reference['relationship_type'] ?? null,
                        'relationship_name' => $reference['relationship_name'] ?? null,
                        'scheme_agency' => $reference['scheme_agency'] ?? null,
                        'attachment_uri' => $reference['attachment'] ?? null,
                        'issue_date_time' => $this->parseDateTime($reference['issue_date_time'] ?? null),
                    ]);
                }
            }
        }
    }

    /**
     * Process products from API data
     */
    private function processProductsFromApi(\App\Models\ChedCertificate $certificate, array $data): void
    {
        if (!isset($data['commodities']) || !is_array($data['commodities'])) {
            return;
        }

        // Clear existing products for this certificate to avoid duplicates
        \App\Models\ChedProduct::where('ched_id', $certificate->ched_id)->delete();

        foreach ($data['commodities'] as $commodity) {
            $classNameEn = null;
            if (isset($commodity['class_names']['en'])) {
                $classNameEn = is_array($commodity['class_names']['en'])
                    ? implode(' | ', $commodity['class_names']['en'])
                    : $commodity['class_names']['en'];
            }

            \App\Models\ChedProduct::create([
                'ched_id' => $certificate->ched_id,
                'system_id' => $commodity['system_id'] ?? null,
                'system_name' => $commodity['system_name'] ?? null,
                'class_code' => $commodity['class_code'] ?? null,
                'class_names' => $commodity['class_names'] ?? null,
                'class_name_en' => $classNameEn,
            ]);
        }
    }

    /**
     * Parse datetime string
     */
    private function parseDateTime(?string $dateTime): ?\Carbon\Carbon
    {
        if (!$dateTime) return null;

        try {
            return \Carbon\Carbon::parse($dateTime);
        } catch (\Exception $e) {
            \Log::warning('Failed to parse datetime', ['datetime' => $dateTime]);
            return null;
        }
    }

    /**
     * Get information about the last sync
     */
    private function getLastSyncInfo(): array
    {
        $lastSync = \App\Models\ChedSyncLog::getLatestSuccessfulSync();

        if (!$lastSync) {
            return [
                'status' => 'no_sync_found',
                'message' => 'No successful sync found. Run: php artisan ched:sync --recent'
            ];
        }

        return [
            'last_sync_time' => $lastSync->sync_end_time->toISOString(),
            'last_sync_human' => $lastSync->sync_end_time->diffForHumans(),
            'certificates_in_last_sync' => $lastSync->total_certificates_fetched,
            'date_range' => $lastSync->date_range_start . ' to ' . $lastSync->date_range_end
        ];
    }



    /**
     * Extract products from TRACES API certificate data
     *
     * @param array $certificate
     * @return array
     */
    private function extractProductsFromTracesData(array $certificate)
    {
        $products = [];
        $consignments = $certificate['consignments'] ?? [];

        \Log::info('Extracting products from certificate data', [
            'consignments_count' => count($consignments),
            'certificate_keys' => array_keys($certificate)
        ]);

        foreach ($consignments as $consignmentIndex => $consignment) {
            $consignmentItems = $consignment['consignment_items'] ?? [];

            \Log::info("Processing consignment $consignmentIndex", [
                'consignment_items_count' => count($consignmentItems),
                'consignment_keys' => array_keys($consignment)
            ]);

            foreach ($consignmentItems as $itemIndex => $item) {
                $sequence = $item['sequence_numeric'] ?? count($products) + 1;
                $classifications = $item['classifications'] ?? [];

                \Log::info("Processing item $itemIndex", [
                    'sequence' => $sequence,
                    'classifications_count' => count($classifications),
                    'item_keys' => array_keys($item)
                ]);

                // Skip sequence 0 as it's usually the totals/summary
                if ($sequence == 0) {
                    \Log::info("Skipping sequence 0 (totals/summary)");
                    continue;
                }

                foreach ($classifications as $classIndex => $classification) {
                    \Log::info("Processing classification $classIndex", [
                        'system_id' => $classification['system_id'] ?? null,
                        'class_code' => $classification['class_code'] ?? null
                    ]);

                    $products[] = [
                        'sequence' => $sequence,
                        'system_id' => $classification['system_id'] ?? null,
                        'system_name' => $classification['system_name'] ?? null,
                        'classification_code' => $classification['class_code'] ?? null,
                        'description' => $classification['class_names']['en'] ?? null,
                        'descriptions' => $classification['class_names'] ?? [],
                        'scientific_name' => $item['scientific_name'] ?? null,
                        'net_weight' => $item['net_weight'] ?? null,
                        'item_description' => $item['description'] ?? null,
                        'source' => 'traces_api'
                    ];
                }

                // If no classifications found, still add the item with basic info
                if (empty($classifications)) {
                    \Log::info("No classifications found for item, adding basic info");
                    $products[] = [
                        'sequence' => $sequence,
                        'system_id' => null,
                        'system_name' => null,
                        'classification_code' => null,
                        'description' => $item['description'] ?? 'No classification data available',
                        'descriptions' => [],
                        'scientific_name' => $item['scientific_name'] ?? null,
                        'net_weight' => $item['net_weight'] ?? null,
                        'item_description' => $item['description'] ?? null,
                        'source' => 'traces_api'
                    ];
                }
            }
        }

        \Log::info('Product extraction completed', ['products_count' => count($products)]);
        return $products;
    }
}
<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CertificateController;


Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');


Route::post('/certificates/fetch', [CertificateController::class, 'fetchAndStore']);

Route::get('/certificates/products', [CertificateController::class, 'getCertificateProducts']);